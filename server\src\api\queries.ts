// TODO: Has this api spelling been fixed?
/* cspell:words Tempate */

import { CaseId, FolderId, TDOId, TreeObjectId } from '../model/brands';
import Config from '../../apiConfig.json';
import { DAG_REFERENCE_IDS } from './constants';

export const fetchIngestedFileStatusQuery = (tdoId: TDOId) => `
  query fetchIngestedFileStatus{
    temporalDataObject(id: ${tdoId}) {
      id
      name
      # Media streamer link
      # primaryAsset(assetType: "media") {
      #   signedUri
      # }
      jobs {
        records {
          id
          status
        }
      }
    }
  }
`;

export const validateTokenQuery = (token: string) => `
  mutation validateToken {
    validateToken(token:"${token}") {
      token
    }
  }
`;

export const getRootFoldersQuery = `
query getRootFolders {
  rootFolders(type: cms) {
    id
  }
}
`;

export const fetchLatestSchemaQuery = (dataRegistryId: string) => `
query fetchLatestSchema {
  dataRegistry(id:"${dataRegistryId}") {
    publishedSchema {
      id
    }
  }
}
`;

export const createFolderQuery = `
mutation createFolder($parentFolderId: ID!, $name: String!, $description: String! ) {
  createFolder(input: {
    parentId: $parentFolderId,
    name: $name,
    description: $description
  }) {
    id
    name
    treeObjectId
  }
}
`;

export const getUserInfoQuery = `
  query getUserInfo {
    me {
      email
    }
  }
`;

export const getAppConfigsQuery = `
  query GetRedactConfig($applicationId: ID!) {
    applicationConfig(appId: $applicationId){
      records {
        configType
        configKey
        value
        valueJSON
      }
    }
  }`;

export const createFolderUsingUserIdQuery = `
mutation createFolder($userId: ID!, $parentFolderId: ID!, $name: String!, $description: String! ) {
  createFolder(input: {
    userId: $userId,
    parentId: $parentFolderId,
    name: $name,
    description: $description
  }) {
    id
    name
    treeObjectId
  }
}
`;

export const createRequestSDOQuery = `
mutation createRequestSDO($name: String!, $folderTreeObjectId: ID!, $currentTime: String!, $schemaId: ID!) {
  createStructuredData(input: {
    id: ""
    schemaId: $schemaId,
    data: {
      caseName: $name,
      status: "new",
      folderTreeObjectId: $folderTreeObjectId,
      createdDateTime: $currentTime,
      modifiedDateTime: $currentTime,
      priority: 1,
      processingTime: 3600
    }
  }) {
    id
    data
  }
}
`;

export const createContentTemplateQuery = `
mutation CreateRequestContentTemplate($schemaId: ID!, $folderId: ID!, $sdoId: ID!) {
  createFolderContentTemplate:createFolderContentTempate(input: {
    folderId: $folderId,
    schemaId: $schemaId,
    sdoId: $sdoId
  }) {
    id
  }
}
`;

export const createRootFolderQuery = `
  mutation createRootFolder {
    createRootFolders(rootFolderType:cms) {
      id
      name
    }
  }
`;

export const checkFolderExistsQuery = (folderId: FolderId) => `
query folder {
  folder(id:"${folderId}") {
    id
    contentTemplates {
      id
    }
    folderPath {
      id
      parent {
        id
      }
    }
  }
}
`;

export const checkNameExistsQuery = (folderId: FolderId,  name:string, limit: number, offset: number) => `
query checkNameExists {
  folder(id:"${folderId}") {
    id
    childFolders(limit:${limit} , offset:${offset}, names:["${name}"], nameMatch:exact){
      count
      records {
        id
        name
        parent {
          id
        }
      }
    }
  }
}
`;

export const createTdoQuery = `
mutation createTDO($input: CreateTDO) {
  createTDO(input: $input) {
    id
  }
}
`;

const ingestionTasks = (
  needOutputFolder: boolean
) => `
    {
      # glc ingestor
      engineId: "${Config.glcIngestionEngineId}"
      payload: $payload
      executionPreferences: {
        priority: -20
      }
      ${
        needOutputFolder
          ? `ioFolders: [
              {
                referenceId: "ingestOutputFolder"
                mode: chunk
                type: output
              }
            ]`
          : ''
      }
    },`;

export const transcriptionChunkerPayload = (chunkLengthSeconds: number) => `{
      comment: "Fast Audio Chunk"
      app: "redact"
      chunkType: "audio"
      chunkDurationSecs: ${chunkLengthSeconds}
      chunkOverlapSecs: 3
    }
`;

export const detectionChunkerPayload = (chunkLengthSeconds: number) => `{
      comment: "Fast Video Chunk"
      app: "redact"
      chunkDurationSecs: ${chunkLengthSeconds}
      chunkOverlapSecs: 3
    }
  `;

export const createIngestJobQuery = (
  runDetection: {
    head?: boolean
    person?: boolean
  },
  runTranscription: boolean,
  transcriptionEngineId: string,
  transcriptionEngineOptions?: Record<string, unknown>,
  featureFlags?: Record<string, boolean>
) => {
  const detectionPayload = detectionChunkerPayload(60);
  const transcriptionPayload = transcriptionChunkerPayload(180);
  const outputWriterEngineId = '8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3';
  const detectionRate = 3;
  const faceDetectionThreshold = 60;
  const videoType = 'Bodycam';

  // TODO: Would be better as a parameter passed to the query
  const transcriptionEnginePayload =
    JSON
      .stringify(Object.assign({}, transcriptionEngineOptions, { diarise: 'false', app: 'redact'}))
      .replace(/"([^"]+)":/g, '$1:');

  return `
  mutation IngestMedia($id: ID!, $payload: JSONData!){
    createJob(input: {
      targetId: $id,
      clusterId: "${Config.defaultClusterId ?? ''}",
      tasks: [
        ${ingestionTasks(runTranscription || !!(runDetection.head || runDetection.person))}
        ${
          runTranscription
            ? `
              {
                engineId: "${Config.fastChunkerEngineId}" # chunk audio
                payload: ${transcriptionPayload}
                executionPreferences: {
                  parentCompleteBeforeStarting: true
                  priority: -5
                }
                ioFolders: [
                  {
                    referenceId: "chunkAudioInputFolder"
                    mode: chunk
                    type: input
                  },
                  {
                    referenceId: "chunkAudioOutputFolder"
                    mode: chunk
                    type: output
                  }
                ]
              },
              {
                engineId: "${transcriptionEngineId}"  # speechmatic
                payload: ${transcriptionEnginePayload}
                executionPreferences: {
                  maxEngines: 10
                  parentCompleteBeforeStarting: true
                  priority: -5
                }
                ioFolders: [
                  {
                    referenceId: "transcriptionInputFolder"
                    mode: chunk
                    type: input
                  },
                  {
                    referenceId: "transcriptionOutputFolder"
                    mode: chunk
                    type: output
                  }
                ]
              },
              {
                engineId: "${outputWriterEngineId}"  # output writer for SM
                executionPreferences: {
                  parentCompleteBeforeStarting: true
                  priority: -15
                }
                ioFolders: [
                  {
                    referenceId: "${DAG_REFERENCE_IDS['owFromTranscription']}"
                    mode: chunk
                    type: input
                  }
                ]
              },`
            : ''
        }
        ${
          runDetection.head || runDetection.person
            ? `
            {
              engineId: "${Config.fastChunkerEngineId}",
              payload: ${detectionPayload}
              executionPreferences: {
                parentCompleteBeforeStarting: true
                priority: -5
              }
              ioFolders: [
                {
                  referenceId: "chunkVideoInputFolder"
                  mode: chunk
                  type: input
                },
                {
                  referenceId: "chunkVideoOutputFolder",
                  mode: chunk,
                  type: output,
                },
              ],
            },
            {
              engineId: "${Config.detectionEngineId}"  # head detection
              payload: {
                confidenceThreshold: ${(faceDetectionThreshold / 100).toFixed(
                  2
                )},
                videoType: "${videoType}",
                stepSizeDetection: ${detectionRate},
                app: "redact",
                detectHead: ${!!runDetection.head},
                detectPerson: ${!!runDetection.person}
                detectLaptop: true,
                detectLicense: true,
                detectVehicle: true,
                detectNotepad: ${(!!featureFlags?.detectNotepads)},
                detectCard: ${(!!featureFlags?.detectCards)},
                mergePersonHead: false,
                mergeVehiclePlate: "plate",
                detectorType: "full"
              }
              executionPreferences: {
                maxEngines: 20
                parentCompleteBeforeStarting: true
                priority: -5
              }
              ioFolders: [
                {
                  referenceId: "hdInputFolder"
                  mode: chunk
                  type: input
                },
                {
                  referenceId: "hdOutputFolder"
                  mode: chunk
                  type: output
                }
              ]
            },
            {
              engineId: "${outputWriterEngineId}"  # output writer for HD
                executionPreferences: {
                  parentCompleteBeforeStarting: true
                  priority: -15
                }
                ioFolders: [
                  {
                    referenceId: "${DAG_REFERENCE_IDS['owFromHead']}"
                    mode: chunk
                    type: input
                  }
                ]
            },`
            : ''
        }
      ],
      routes: [
        ${
          runTranscription
            ? `
            {  ## ingest --> chunkAudio
              parentIoFolderReferenceId: "ingestOutputFolder"
              childIoFolderReferenceId: "chunkAudioInputFolder"
              options: {}
            },
            {  ## chunkAudio --> Transcription
              parentIoFolderReferenceId: "chunkAudioOutputFolder"
              childIoFolderReferenceId: "transcriptionInputFolder"
              options: {}
            },
            {  ## Transcription --> output writer
              parentIoFolderReferenceId: "transcriptionOutputFolder"
              childIoFolderReferenceId: "${DAG_REFERENCE_IDS['owFromTranscription']}"
              options: {}
            },`
            : ''
        }
        ${
          runDetection.head || runDetection.person
            ? `
            { ## ingest to chunkVideo
              parentIoFolderReferenceId: "ingestOutputFolder"
              childIoFolderReferenceId: "chunkVideoInputFolder"
              options: {}
            },
            {  ## chunkVideo --> Head Detection
              parentIoFolderReferenceId: "chunkVideoOutputFolder"
              childIoFolderReferenceId: "hdInputFolder"
              options: {}
            },
            {  ## HeadDetection --> output writer
              parentIoFolderReferenceId: "hdOutputFolder"
              childIoFolderReferenceId: "${DAG_REFERENCE_IDS['owFromHead']}"
              options: {}
            },`
            : ''
        }
      ]
    }) {
      id
      targetId
      status
    }
  }`;
}

export const deleteFolderQuery = (folderId: TreeObjectId) => `
  mutation deleteFolder {
    deleteFolder(input: {
      id:"${folderId}",
      orderIndex: 0,
    }) {
      id
      message
    }
  }
`;

export const fetchCaseFileListQuery = (caseId: CaseId, limit: number, offset: number) => `
query fetchRequestFileListQuery {
  case: folder(id:"${caseId}") {
    name
    files: childTDOs(limit:${limit}, offset:${offset}) {
      records {
        id
        name
        # This is a media streamer link -- do we REALLY want people using this?
        # primaryAsset(assetType: "media") {
        #   signedUri
        # }
      }
    }
  }
}
`;

export const redactedMediaQuery = (tdoId: TDOId) => `
query RedactedMediaQuery  {
  temporalDataObject: temporalDataObject(id:${tdoId}) {
    redactedMediaAssets: assets(
      assetType: "redacted-media"
      orderBy: createdDateTime
      limit: 1
    ) {
      records {
        type: assetType
        signedUri
        createdDateTime
      }
    }
    auditLogAssets: assets(
      assetType: ["redact-audit-log", "blur-audit-log"]
      orderDirection: desc
      orderBy: createdDateTime
      limit: 1
    ) {
      records {
        type: assetType
        signedUri
        createdDateTime
      }
    }
    redactedTranscriptAssets: assets(
      assetType: ["redacted-transcript"]
      orderDirection: desc
      orderBy: createdDateTime
      limit: 1
    ) {
      records {
        type: assetType
        signedUri
        createdDateTime
      }
    }
  }
}
`;

export const deleteFileQuery = (tdoId: TDOId) => `
  mutation deleteFile {
    deleteTDO(id:${tdoId}) {
      id
      message
    }
  }
`;

export const getSdoIdQuery = (folderId: FolderId) => `
  query getSdoId {
    folder(id:"${folderId}") {
      id
      treeObjectId
      contentTemplates {
        sdoId
      }
    }
  }
`;
export const deleteSdoQuery = (sdoId: string, schemaId: string) => `
  mutation deleteSdo {
    deleteStructuredData(input: { id:"${sdoId}", schemaId:"${schemaId}" }) {
      id
    }
  }
`;

export const mediaFileDetailsQuery = (tdoId: TDOId) => `
query MediaFileDetailsQuery  {
  temporalDataObject: temporalDataObject(id:${tdoId}) {
    thumbnailUrl
    # primaryAssets: assets(
    #  assetType: "media"
    #  orderBy: createdDateTime
    #  limit: 1
    # ) {
    #  records {
    #    type: assetType
    #    signedUri
    #    createdDateTime
    #  }
    # }
    redactedMediaAssets: assets(
      assetType: "redacted-media"
      orderBy: createdDateTime
      limit: 1
    ) {
      records {
        type: assetType
        signedUri
        createdDateTime
      }
    }
    auditLogAssets: assets(
      assetType: ["redact-audit-log", "blur-audit-log"]
      orderDirection: desc
      orderBy: createdDateTime
      limit: 1
    ) {
      records {
        type: assetType
        signedUri
        createdDateTime
      }
    }
    redactedTranscriptAssets: assets(
      assetType: ["redacted-transcript"]
      orderDirection: desc
      orderBy: createdDateTime
      limit: 1
    ) {
      records {
        type: assetType
        signedUri
        createdDateTime
      }
    }
    details
  }
}
`;

export const findTdoLockQuery = `
  query findTdoLock($schemaId: ID!, $tdoId: ID!) {
    structuredDataObjects(
      schemaId: $schemaId
      filter: { tdoId: $tdoId }
      orderBy: {
        field: modifiedDateTime
        direction: asc
      }
    ) {
      records {
        id
        data
      }
    }
  }`;

export const getFilesByTreeObjectIdQuery = `
query searchMedia($treeObjectId: ID!, $limit:Int, $offset:Int) {
  searchMedia(search: {
      index: ["mine"],
      select: ["veritone-file"],
      limit: $limit,
      offset: $offset,
      query: {
        operator: "and",
        conditions:[
          {
            field: "parentTreeObjectIds",
            operator: "terms",
            values: [$treeObjectId]
          }
        ]
      }
    }) {
    jsondata
  }
}
`


export const getFilesByCaseIdQuery = `
query getFilesByCaseId($caseId: ID!, $limit: Int, $offset: Int) {
  folder(id:$caseId) {
    id
    childTDOs(limit:$limit, offset:$offset) {
      records {
        id
        jobs {
          records {
            status
          }
        }
      }
    }
  }
}
`;

export const getChildFoldersQuery = `
query getChildFolders ($folderId: ID!, $limit: Int, $offset: Int) {
  folder(id:$folderId) {
    id
    childFolders(limit:$limit , offset:$offset){
      records {
        id
       contentTemplates {
          id
        }
      }
    }
  }
}
`;
