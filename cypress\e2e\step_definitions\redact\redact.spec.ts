import {
  Given,
  When,
  Then,
  Before,
} from '@badeball/cypress-cucumber-preprocessor';
import {
  Colors,
  DataTestSelector,
  DataVeritoneSelector,
  Graphql,
  VideoName,
  waitForJobSuccess,
} from '../../../support/helperFunction/mediaDetailHelper';
import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
  (cy as any).intercept('POST', '/v3/graphql', (req: any) => {
    if (req.body.query === Graphql.JobQueryNoTargetId) {
      req.alias = 'jobStatus';
    }
  });
});

Given('I have redacted the transcription', () => {
  const transcriptionContainer = '[data-testid="transcription-view-container"]';
  let redactWord: string;

  cy.getDataIdCy({ idAlias: DataTestSelector.audioTabBtn }).click();
  cy.get(DataVeritoneSelector.randomWord)
    .should('exist')
    .invoke('text')
    .then((text) => {
      redactWord = text.trim();
      cy.get(DataVeritoneSelector.randomWord).rightclick();
      cy.get('li').contains('Redact').click();
      cy.get(DataVeritoneSelector.randomWord)
        .should('have.css', 'color', Colors.Gray)
        .and('have.css', 'background-color', Colors.Black);
      cy.get(DataVeritoneSelector.randomWord).rightclick();
      cy.get('li').contains('Unredact').click();
      cy.get(DataVeritoneSelector.randomWord).rightclick();
      cy.get('li').contains('Redact').click();

      cy.get(transcriptionContainer)
        .find('span')
        .contains(redactWord)
        .should('have.css', 'color', Colors.Gray)
        .and('have.css', 'background-color', Colors.Black);
      return;
    });
});

When(
  'I redact the file using coordinates {int}, {int}, {int}, {int}',
  (x1: number, y1: number, x2: number, y2: number) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.videoTabBtn }).click();
    // cy.get(videoTab).click();
    cy.DrawAnUdr({ orderNumber: 0, x1, y1, x2, y2 });
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabSaveBtn })
      .should('be.visible')
      .click();
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabRedactBtn })
      .should('not.be.disabled')
      .click();

    // Wait for the redaction job to complete need this for low ram pc can test
    // Optionally verify completion:
    // cy.CheckJobComplete(VIDEO_NAME, REDACTION);
  }
);

Then('I should see the redacted file and be able to download it', () => {
  const downloadBtn =
    '[data-veritone-element="redacted-files-download-button"]';

  waitForJobSuccess({
    maxRetryAttempts: 30,
  });
  cy.get('[data-veritone-element="redacted-files-tab-button"]').click();
  cy.get('[data-veritone-element="filename"]').should(
    'contain',
    VideoName.TestVideo
  );
  cy.get(downloadBtn).click();
  cy.get(downloadBtn).should('not.have.attr', 'disable');
  cy.get('[data-veritone-element="media-details-close-button"]').click();
  cy.contains(
    '[data-veritone-element="tdo-wrapper-details"]',
    VideoName.TestVideo
  );
});

When('The user clicks the {string} button', (btnName: string) => {
  cy.get('[data-testid="results-tab-redact-file-button"]')
    .contains(btnName)
    .click();
});

Then(
  'The button {string} changes to {string}',
  (btnOldName: string, btnNewName: string) => {
    cy.getDataIdCy({ idAlias: 'results-tab-redact-file-button' }).as(
      'redactButton'
    );

    cy.get('@redactButton').contains(btnOldName).should('not.exist');
    cy.get('@redactButton')
      .contains(btnNewName)
      .should('exist')
      .should('be.visible');
  }
);

Then('The confirmation dialog is displayed', () => {
  cy.getDataIdCy({ idAlias: 'confirm-dialog-title' })
    .parent()
    .should('be.visible');
});

When('The user confirms {string} to start redaction', (btnName: string) => {
  cy.getDataIdCy({ idAlias: 'confirm-dialog-button' })
    .contains(btnName)
    .click();
});

When('The user waits for redaction to complete', () => {
  waitForJobSuccess({
    maxRetryAttempts: 30,
  });
  cy.get('#notistack-snackbar', { timeout: 120000 })
    .should('be.visible')
    .and(
      'contain.text',
      'File redacted successfully. Click View File to review.'
    );
  cy.get('#notistack-snackbar', { timeout: 10000 }).should('not.exist');
});

When('The user navigates to Redacted Files tab', () => {
  cy.getDataIdCy({ idAlias: 'redacted-files-tab-button' }).click();
  cy.getDataIdCy({ idAlias: 'redacted-files-tab-button' }).should(
    'have.attr',
    'aria-selected',
    'true'
  );
});

Then('The user should see the video player in Redacted Files tab', () => {
  cy.getDataIdCy({ idAlias: 'redacted-files-tab' })
    .should('be.visible')
    .within(() => {
      cy.getDataIdCy({ idAlias: 'redacted-files-grid-8' })
        .find('.video-react-video')
        .should('be.visible');

      cy.get('.video-react-control-bar').should('exist');

      cy.get('.video-react-big-play-button').should('be.visible');
    });
});

When('The user clicks on down button next to Redact Files button', () => {
  cy.get('#media-detail-tab').scrollTo('right', { ensureScrollable: false });
  cy.getDataIdCy({ idAlias: 'results-tab-redact-file-button-group' }).within(
    () => {
      cy.getDataIdCy({ idAlias: 'ExpandMoreIcon' }).click();
    }
  );
});

Then('The user should see the menu displayed', () => {
  cy.get('#split-button-menu').should('be.visible');
});

Then('The user choose {string} option', (option: string) => {
  cy.get('#split-button-menu').contains(option).click();
});

Then('The user should see the trim range dialog displayed', () => {
  cy.getDataIdCy({ idAlias: 'confirm-dialog-title' })
    .parent()
    .should('be.visible');
});

Then(
  'The user enter {string} time at {string} {string}',
  (time: string, value: string, timeType: string) => {
    cy.get(
      `[data-testid="time-stamp-range-editor-popup-${time}-${timeType}"]`
    ).as('time');
    cy.get('@time').clear();
    cy.get('@time').type(`${value}`);
  }
);

Then('The user click button Set to set time for trim', () => {
  cy.getDataIdCy({ idAlias: 'confirm-dialog-button' }).contains('Set').click();
});

Then(
  'The error message {string} should be displayed for {string}',
  (errorMessage: string, timeType: string) => {
    cy.get(
      `[data-testid="time-stamp-range-editor-popup-time-input-title"]:contains("${timeType}")`
    )
      .parent()
      .parent()
      .find('p')
      .should('contain.text', errorMessage);
  }
);

Then(
  'The user should see the version {int} in redacted files',
  (version: number) => {
    cy.get('[data-veritone-element="fileVersion"]')
      .should('be.visible')
      .contains(`Version: ${version}`, { timeout: 60000 });
  }
);

When('The user deletes Redact File {string} if exist', (videoName: string) => {
  cy.visit('/');
  cy.GoToTestFile(videoName);
  cy.get(
    `[data-veritone-element="${DataVeritoneSelector.RedactFileTabBtn}"]`
  ).click();
  cy.get(
    `[data-veritone-element="${DataVeritoneSelector.RedactFileDelBtn}"]`
  ).click();
  cy.getDataIdCy({ idAlias: DataTestSelector.RedactModalDelBtn }).click();
  cy.get(
    `[data-veritone-element="${DataVeritoneSelector.MediaDetailCloseBtn}"]`
  ).click();
});

When('The user opens delete Redact modal', () => {
  cy.get(
    `[data-veritone-element="${DataVeritoneSelector.RedactFileDelBtn}"]`
  ).click();
});

When('The user confirms deletion of Redact File', () => {
  cy.getDataIdCy({ idAlias: DataTestSelector.RedactModalDelBtn }).click();
});

When('The user removes all redacted files', () => {
  cy.url().then((currentUrl) => {
    const parts = currentUrl.split('/');
    const tdoId = parts[parts.length - 1] || '';
    cy.clearTrimRange(tdoId);
    return cy.deleteAllRedacted(tdoId);
  });
});

When('The user removes current tdoFile', () => {
  cy.url().then((currentUrl) => {
    const parts = currentUrl.split('/');
    const tdoId = parts[parts.length - 1];
    cy.get(
      `[data-veritone-element="${DataVeritoneSelector.MediaDetailCloseBtn}"]`
    ).click();
    cy.get(`[data-tdo-id="${tdoId}"]`).within(() => {
      cy.getDataIdCy({ idAlias: '@mainpage-tdo-checkbox' })
        .get('input[type="checkbox"]')
        .first()
        .click({ force: true });
    });
    cy.getDataIdCy({ idAlias: '@delete-tdos-button' }).click();
    cy.getDataIdCy({ idAlias: '@confirm-delete-tdos-ok-button' }).click();
    return;
  });
});

Then('The report should have name {string}', (title: string) => {
  cy.readFile(`cypress/downloads/${title}`).should('exist');
});

Then(
  'The report should have name {string} with detail {string}',
  (fileName: string, title: string) => {
    cy.readFile(`cypress/downloads/${fileName}`).should('include', title);
  }
);

Then('The user checks all checkbox', () => {
  cy.get('[data-testid="check-box-container"]').first().click();
});

Then(
  'Delete modal shows {int} previous versions remaining',
  (expectedPreviousVersions: number) => {
    cy.getByRoles('dialog')
      .find('p')
      .should(
        'contain.text',
        'Would you like to delete the latest redacted file?'
      )
      .invoke('text')
      .should(
        'match',
        new RegExp(
          `${expectedPreviousVersions} previous version\\(s\\) remain.`
        )
      );
  }
);

Then('The Redacted Files tab should be disabled', () => {
  cy.get('[data-testid="redacted-files-tab-button"]')
    .should('exist')
    .should('have.attr', 'disabled');
});

Then('The Redacted Files tab should have color {string}', (color: string) => {
  cy.get('[data-testid="redacted-files-tab-button"]')
    .children()
    .first()
    .should('have.css', 'color', color);
});

Then('The user should see all elements in the Redacted Files tab', () =>
  [
    () => cy.get('.video-react-video').should('be.visible'),
    () => cy.contains('Latest Redacted File').should('be.visible'),
    () =>
      cy
        .get('[data-veritone-element="fileVersion"]')
        .should('be.visible')
        .and('contain', 'Version:'),
    () =>
      cy
        .get('[data-veritone-element="filename"]')
        .should('be.visible')
        .and('contain', '[REDACTED]')
        .and('contain', '.mp4'),
    () =>
      cy.get('[data-veritone-element="modifiedDateTime"]').should('be.visible'),
    () => cy.get('.icon-face').should('be.visible'),
    () =>
      [
        'Download All',
        'Download Media',
        'DOWNLOAD AUDIT LOG',
        'Delete',
      ].forEach((l) => cy.contains('button', l).should('be.visible')),
    // () => cy.contains('button', 'DOWNLOAD AUDIT LOG').should('exist'),
  ].forEach((f) => f())
);

Then('The delete confirm dialog should display', () => {
  cy.get('[role="dialog"]').should('be.visible');
});

Then('The user choose {string} to delete redacted file', (btnName: string) => {
  cy.get(`[data-testid="delete-redaction-modal-${btnName}-button"]`).click();
});

Then('The user clicks delete button in Redacted Files tab', () => {
  cy.get('[data-veritone-element="redacted-files-delete-button"]').click();
});

Then(
  'The user clicks {string} button in Redacted Files tab',
  (btnName: string) => {
    cy.contains('button', btnName).should('be.visible').click();
  }
);

When('The user clicks on the preview video', () => {
  cy.get('button[type="button"][aria-live="polite"]').click();
});

Then('The video should contain the redacted UDR', () => {
  cy.get('[data-testid="redacted-files-grid-8"] .video-react-video')
    .should('be.visible')
    .invoke('attr', 'src')
    .then((src): string => {
      if (typeof src !== 'string') {
        throw new Error('Video src attribute is missing');
      }

      const decoded = decodeURIComponent(src);
      const match = decoded.match(/filename="(.+)"/);
      if (!match) {
        throw new Error('Filename not found in video source URL');
      }

      const filename = decodeURIComponent(match[1] || '');
      if (!filename.includes('[REDACTED]')) {
        throw new Error(`Filename "${filename}" does not include "[REDACTED]"`);
      }

      return filename;
    });
});

Then('The user see number of view is {int}', (viewCount: number) => {
  cy.get('[data-veritone-element="metrics-views"]')
    .should('be.visible')
    .contains(viewCount);
});

Then('The user close the redacted files', () => {
  cy.get('[data-veritone-element="media-details-close-button"]').click();
});
