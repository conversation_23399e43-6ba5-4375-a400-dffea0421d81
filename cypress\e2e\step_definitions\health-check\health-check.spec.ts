import { Before, Given } from '@badeball/cypress-cucumber-preprocessor';
import { DataTestSelector } from '../../../support/helperFunction/landingPageHelper';
import { Graphql } from '../../../support/helperFunction/mediaDetailHelper';
import { RedactionCodeGraphQlQuery } from '../../../support/helperFunction/redactionCodeHelper';
import { ProfileGraphQlQuery } from '../../../support/helperFunction/profilesPageHelper';

Before(() => {
  cy.LoginLandingPage();
  (cy as any).intercept('POST', Graphql.GraphqlURL, (req: any) => {
    if (req.body.query === Graphql.JobQuery) {
      req.alias = 'jobStatus';
    }
  });
  cy.interceptGraphQLQuery(
    RedactionCodeGraphQlQuery.FetchRedactionCode,
    'fetchRedactionCode'
  );
  cy.interceptGraphQLQuery(ProfileGraphQlQuery.CreateProfile, 'createProfile');
  cy.interceptGraphQLQuery(ProfileGraphQlQuery.UpdateProfile, 'updateProfile');
});

Given('The user verify Landing Page', () => {
  cy.contains('Upload Media').should('be.visible');
  cy.contains('Sort by').should('be.visible');
  cy.getDataSetCy({ cyAlias: DataTestSelector.TopBar }).contains(
    'Last Modified Date'
  );
  cy.getDataIdCy({ idAlias: DataTestSelector.Grid }).within(() => {
    cy.getDataIdCy({ idAlias: DataTestSelector.Checkbox }).should('be.visible');
    cy.getDataIdCy({ idAlias: DataTestSelector.FileName }).should('be.visible');
    cy.getDataSetCy({ cyAlias: DataTestSelector.DateTime }).should(
      'be.visible'
    );
    cy.getDataIdCy({ idAlias: DataTestSelector.Status }).should('be.visible');
  });
});

Given('The user verify Redaction Code screen', () => {
  cy.visit('/redaction-codes'); // TODO fixture only Redaction Code page
  cy.awaitNetworkResponseCode({ alias: '@fetchRedactionCode', code: 200 });
  cy.getDataIdCy({ idAlias: 'redaction-codes-title' })
    .contains('Redaction Codes')
    .should('be.visible');
});

Given('The user verify Profiles screen', () => {
  cy.visit('/settings-profile');
  cy.url().should('include', 'settings-profile');
  cy.getDataIdCy({ idAlias: 'profile-settings-title' }).should(
    'contain.text',
    'Profiles'
  );
  cy.get('button').contains('Add New Profile').should('be.visible');
});
