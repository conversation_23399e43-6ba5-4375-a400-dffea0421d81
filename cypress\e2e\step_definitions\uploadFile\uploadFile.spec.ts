import { Before, Given, Then } from '@badeball/cypress-cucumber-preprocessor';
import {
  Graphql,
  INGESTION,
  VideoName,
  waitForJobSuccess,
} from '../../../support/helperFunction/mediaDetailHelper';
import { DataTestSelector } from '../../../support/helperFunction/landingPageHelper';
import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
  (cy as any).intercept('POST', Graphql.GraphqlURL, (req: any) => {
    if (req.body.query === Graphql.JobQuery) {
      req.alias = 'jobStatus';
    }
  });
});

Given('Upload without run any engine', () => {
  cy.UploadFileWithoutEngine({ videoName: VideoName.TestVideo });
  cy.get('@tdoIdValue').then((targetId) => {
    waitForJobSuccess({
      maxRetryAttempts: 20,
      retryCount: 0,
      _targetId: targetId,
    });
    return;
  });
  cy.CheckJobCompleteDashboard({
    videoName: VideoName.TestVideo,
    jobName: INGESTION,
  });
});

Given('Upload with run engines', () => {
  cy.UploadFileWithEngine({ videoName: VideoName.TestVideo });
  cy.get('@tdoIdValue').then((targetId) => {
    waitForJobSuccess({
      maxRetryAttempts: 20,
      retryCount: 0,
      _targetId: targetId,
    });
    return;
  });
  cy.CheckJobCompleteDashboard({
    videoName: VideoName.TestVideo,
    jobName: INGESTION,
  });
  cy.getDataIdCy({ idAlias: DataTestSelector.Status })
    .first()
    .contains('Draft');
  cy.get('[data-veritone-element="tdo-wrapper-details"]')
    .first()
    .children('[data-test="icon-list-out"]')
    .find('[data-test="icon"]')
    .within(() => {
      cy.get('.icon-face').its(0).should('be.visible');
      cy.get('.icon-transcription').its(0).should('be.visible');
    });
});

Then(
  'The user selects and deletes the uploaded file {string}',
  (fileName: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.FileName })
      .first()
      .contains(fileName);
    cy.getDataIdCy({ idAlias: DataTestSelector.Checkbox })
      .get('input[type="checkbox"]')
      .first()
      .click({ force: true });
    cy.getDataIdCy({ idAlias: DataTestSelector.DelBtn }).click();
    cy.getDataIdCy({ idAlias: DataTestSelector.ConfirmDelBtn }).click();
  }
);
