import {
  Then,
  Given,
  When,
  Before,
} from '@badeball/cypress-cucumber-preprocessor';
import {
  RedactionCodeGraphQlQuery,
  RSortedBy,
  RTableHeader,
  RowPerPage,
  RedactionCodeText,
  RedactionCodeDataSelector,
} from '../../../support/helperFunction/redactionCodeHelper';

Before(() => {
  cy.LoginToApp();
  cy.interceptGraphQLQuery(
    RedactionCodeGraphQlQuery.FetchRedactionCode,
    'fetchRedactionCode'
  );
});

Given('The user is on the Redaction Code screen', () => {
  cy.visit('/redaction-codes'); // TODO fixture only Redaction Code page
  cy.awaitNetworkResponseCode({ alias: '@fetchRedactionCode', code: 200 });
  cy.getDataIdCy({ idAlias: 'redaction-codes-title' })
    .contains('Redaction Codes')
    .should('be.visible');
});

Given('The user clicks on Add New Code button', () => {
  cy.getDataIdCy({
    idAlias: RedactionCodeDataSelector.AddNewCodeIconButton,
  }).click();
});

Given(
  'The user deletes redaction code {string} if exists',
  (codeName: string) => {
    cy.deleteRedactionCodeIfExist(codeName);
  }
);

When(
  'The user inputs Redaction code with code name: {string}, description: {string} and code number: {string}',
  (codeName: string, description: string, codeNumber: string) => {
    cy.get(`input[placeholder="${RedactionCodeText.CodeNamePlaceholder}"]`).as(
      'codeNameInput'
    );
    cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.DescriptionInput }).as(
      'description-input'
    );
    cy.get(
      `input[placeholder="${RedactionCodeText.RedactionCodePlaceholder}"]`
    ).as('codeNumberInput');

    cy.get('@codeNameInput').clear();
    cy.get('@codeNameInput').type(codeName);

    cy.get('@description-input').clear();
    cy.get('@description-input').type(description);

    cy.get('@codeNumberInput').clear();
    cy.get('@codeNumberInput').type(codeNumber);
  }
);

Then('Redaction code sidebar should be displayed', () => {
  cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.Drawer }).should(
    'be.visible'
  );
});

When('User clicks on Add button', () => {
  cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.AddBtn }).click();
});

Then('Redaction code {string} should be displayed', (codeName: string) => {
  cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.Table })
    .find('tr')
    .filter(`:has(td:contains("${codeName}"))`)
    .as('codeNameRow');
  cy.get('@codeNameRow').should('be.visible');
});

Then(
  'The user sees the notification message {string}',
  (messageText: string) => {
    cy.get('#notistack-snackbar').should('contain.text', messageText);
  }
);

Given('The user clicks the {string} button', (btnName: string) => {
  cy.get('@codeNameRow').within(() => {
    cy.get('[data-testid^="redaction-code-table-tableMenuIcon"]').click();
  });
  cy.get('[data-testid="redaction-codes-table-menu-icon"]')
    .filter(':visible')
    .within(() => {
      cy.getByRoles('menuitem').contains(btnName).click();
    });
});

Then(
  'The Redaction code should be displayed with code name: {string}, description: {string} and code number: {string}',
  (codeName: string, description: string, codeNumber: string) => {
    cy.get('@codeNameInput').should('have.value', codeName);
    cy.get('@description-input').should('contain.text', description);
    cy.get('@codeNumberInput').should('have.value', codeNumber);
  }
);

Then(
  'A pop-up with {string} and {string} is shown',
  (Edit: string, Delete: string) => {
    cy.get('.MuiPopover-paper')
      .filter((_index, element) => Cypress.$(element).css('opacity') === '1')
      .within(() => {
        cy.contains(Edit).should('exist');
        cy.contains(Delete).should('exist');
      });
  }
);

Given('User selects {string}', (Edit: string) => {
  cy.get('.MuiPopover-paper')
    .filter((_index, element) => Cypress.$(element).css('opacity') === '1')
    .contains(Edit)
    .click();
});

When('User clicks on Save button', () => {
  cy.get('button').contains(RedactionCodeText.SaveBtn).click();
});

When('The user clicks on {string} confirm button', (btnName: string) => {
  cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.ConfirmBtn })
    .contains(btnName)
    .click();
});

When(
  'The instructor presses the {string} sort button to sort by {string}',
  (label: RTableHeader, orderBy: RSortedBy) => {
    cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.Table })
      .find('thead th span[role="button"]')
      .filter((_index, element) => {
        const $button = Cypress.$(element);

        const buttonText = $button
          .contents()
          .filter(function () {
            return this.nodeType === Node.TEXT_NODE;
          })
          .text()
          .trim();

        return buttonText === label;
      })
      .as('tableHeading');
    cy.get('@tableHeading')
      .parent()
      .then(($heading) => {
        if ($heading.attr('aria-sort') && orderBy === 'a-z') {
          return;
        } else if (!$heading.attr('aria-sort') && orderBy === 'z-a') {
          cy.get('@tableHeading').trigger('mouseover');
          cy.get('@tableHeading').click({ scrollBehavior: false, force: true });
          cy.wrap($heading).should('have.attr', 'aria-sort', 'ascending');
          cy.get('@tableHeading').click({ scrollBehavior: false });
          return;
        } else {
          return cy.get('@tableHeading').click({ scrollBehavior: false });
        }
      });
  }
);

Then(
  '{string} is sorted by {string}',
  (label: RTableHeader, orderBy: RSortedBy) => {
    cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.Table })
      .find('thead')
      .find(`th:contains("${label}")`)
      .then(($th) => {
        const columnIndex = $th.index();

        return cy
          .getDataIdCy({ idAlias: RedactionCodeDataSelector.Table })
          .find('tbody tr')
          .then(($rows) => {
            const cellTexts: string[] = [];
            const promises: any[] = [];

            const processRow = (row: HTMLElement) =>
              cy
                .wrap(row)
                .find(`td:eq(${columnIndex})`)
                .then(($td) => cellTexts.push($td.text().trim()));

            $rows.each((_index, row) => {
              promises.push(processRow(row));
            });

            return Cypress.Promise.all(promises).then(() => {
              const sortedTexts = cellTexts.toSorted();
              if (orderBy === 'z-a') {
                return sortedTexts.reverse();
              }
              return expect(cellTexts).to.deep.equal(sortedTexts);
            });
          });
      });
  }
);

Then('Delete Redaction dialog is shown', () => {
  cy.getDataIdCy({ idAlias: RedactionCodeDataSelector.ConfirmTitle })
    .contains(RedactionCodeText.DeleteCode)
    .should('be.visible');
});

Given('The user selects Row per page to {int}', (rowPerPage: RowPerPage) => {
  cy.get('div[role="combobox"][aria-haspopup="listbox"]').click();
  cy.get(
    `ul[role="listbox"] li[data-value="${rowPerPage.toString()}"]`
  ).click();
});

Then(
  'The total number of rows displayed is up to {int}',
  (rowPerPage: RowPerPage) => {
    cy.get('tr').should(
      'have.length.at.most',
      parseInt(rowPerPage.toString()) + 1
    );
  }
);

When('The user moves the navigation bar {string}', (scrollTo: string) => {
  cy.get('table').parent().as('redactionCodeScroll');
  cy.get('@redactionCodeScroll').scrollTo(
    scrollTo === 'down' ? 'bottom' : 'top'
  );
});

Then('The navigation bar should be scrolled {string}', (scrollTo: string) => {
  cy.get('@redactionCodeScroll').should(($el) => {
    if (scrollTo === 'down') {
      expect(($el[0] as HTMLElement).scrollTop).to.be.greaterThan(0);
    } else {
      expect(($el[0] as HTMLElement).scrollTop).to.equal(0);
    }
  });
});

When(
  'The user clicks the {string} button in the pagination section',
  (btnName: string) => {
    if (btnName === '>') {
      cy.get(
        `button[aria-label="${RedactionCodeText.RightArrowButtonTitle}"]`
      ).click();
    } else {
      cy.get(
        `button[aria-label="${RedactionCodeText.LeftArrowButtonTitle}"]`
      ).click();
    }
  }
);

Then('The user should go to page: {string}', (range: string) => {
  cy.get('div.MuiTablePagination-root').contains('p', range).should('exist');
});
