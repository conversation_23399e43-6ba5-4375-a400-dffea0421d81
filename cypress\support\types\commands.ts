// Cypress Command Types

export interface UDRCoordinates {
  orderNumber: number;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface RepeatOptions {
  action: () => void;
  times: number;
}

export interface NetworkResponseOptions {
  alias: string;
  code: number;
  repeat?: number;
}

export interface DataIdCyOptions {
  idAlias: string;
  options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
}

export interface DataSetCyOptions {
  cyAlias: string;
  options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
}

export interface NavigationOptions {
  sectionName: string;
}

export interface FileUploadOptions {
  videoName: string;
}

export interface FileAttachmentOptions {
  fileContent: Blob;
  fileName: string;
  mimeType: string;
  encoding: string;
}

// Removed unused interfaces - KeyValueOptions and FilterByAriaOptions

// Media Player Types
export interface MediaPlayerState {
  currentTime: number;
  duration: number;
  paused: boolean;
  volume: number;
}

// Store State Types (for Redux store access)
export interface StoreState {
  [key: string]: unknown;
}

// Window with Store
export interface WindowWithStore {
  store: {
    getState: () => StoreState;
    dispatch: (action: unknown) => void;
  };
}

// File Upload Types
export interface UploadFileContent {
  fileContent: Blob;
  fileName: string;
  mimeType: string;
  encoding?: string;
}

// GraphQL Variables
export interface GraphQLVariables {
  [key: string]: unknown;
}

// Custom Cypress Types for better type safety
export type CypressChainable<T = unknown> = Cypress.Chainable<T>;
export type CypressElement = Cypress.Chainable<JQuery<HTMLElement>>;
export type CypressSubject = JQuery<HTMLElement>;

// Task Types for Cypress tasks
export interface CypressTaskOptions {
  [key: string]: unknown;
}

// Intercept Types
export interface GraphQLInterceptRequest {
  body: {
    query: string;
    variables?: GraphQLVariables;
  };
  url: string;
  alias?: string;
  continue: () => void;
}

// Removed unused interfaces - CSSPropertyOptions, RoleOptions, VideoControlOptions

// Removed unused types - DrawUDROptions, LoginResult, GraphQLResult, ElementResult, VoidResult, CustomAssertions

// Removed unused types - CypressEnv, FixtureData, TestContext, WaitForJobOptions, InterpolationValues, TimeRange, VideoResult, RedactionEffect, DataTestSelector, Colors, CSSElement
