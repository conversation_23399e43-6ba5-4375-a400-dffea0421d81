{"compilerOptions": {"target": "es2023", "lib": ["es2023", "dom"], "module": "ESNext", "moduleResolution": "node", "baseUrl": "./", "skipLibCheck": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "types": ["cypress", "node", "chai"], "typeRoots": ["../node_modules/@types", "../node_modules/cypress/types", "./support/types"]}, "include": ["**/*.js", "**/*.ts", "**/*.tsx", "support/index.d.ts", "support/types/**/*"], "exclude": ["node_modules", "reports", "downloads", "screenshots"]}