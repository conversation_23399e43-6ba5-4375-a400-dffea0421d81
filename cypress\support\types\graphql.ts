// GraphQL Response Types for Cypress Tests

export interface GraphQLResponse<T = unknown> {
  data: T;
  errors?: Array<{
    message: string;
    locations?: Array<{
      line: number;
      column: number;
    }>;
    path?: Array<string | number>;
  }>;
}

export interface CypressResponse<T = unknown> {
  body: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// Asset Types
export interface Asset {
  id: string;
  assetType: string;
  isUserEdited?: boolean;
}

export interface AssetRecord {
  id: string;
  assetType:
    | 'redaction-user-selections'
    | 'redaction-media-data'
    | 'redacted-media'
    | 'redaction-audit-log'
    | 'redaction-export';
  isUserEdited?: boolean;
}

export interface AssetsResponse {
  records: AssetRecord[];
  count?: number;
}

// TDO (Temporal Data Object) Types
export interface TDOAssets {
  temporalDataObject: {
    assets: AssetsResponse;
  };
}

export interface TDODetails {
  redact?: {
    tasks: unknown[];
  };
  [key: string]: unknown;
}

export interface TDODetailResponse {
  temporalDataObject: {
    details: TDODetails;
  };
}

export interface UpdateSettingsTDOResponse {
  updateTDO: {
    id: string;
  };
}

export interface TDODownloadResponse {
  temporalDataObject: {
    assets: {
      records: AssetRecord[];
    };
  };
}

// Job Types
export interface JobResponse {
  job: {
    status: string;
    createdDateTime: string;
  };
}

// Profile Types
export interface ProfileData {
  profileName: string;
  [key: string]: unknown;
}

export interface ProfileRecord {
  id: string;
  createdDateTime: string;
  modifiedDateTime?: string;
  data: ProfileData;
}

export interface FetchProfilesResponse {
  structuredDataObjects: {
    count: number;
    records: ProfileRecord[];
  };
}

export interface DeleteProfileResponse {
  deleteStructuredData: {
    id: string;
  };
}

// Redaction Code Types
export interface RedactionCodeData {
  codeName: string;
  [key: string]: unknown;
}

export interface RedactionCodeRecord {
  id: string;
  createdDateTime: string;
  modifiedDateTime?: string;
  data: RedactionCodeData;
}

export interface FetchRedactionCodeResponse {
  structuredDataObjects: {
    count: number;
    records: RedactionCodeRecord[];
  };
}

export interface DeleteRedactionCodeResponse {
  deleteStructuredData: {
    id: string;
  };
}

// Organization SDO Types
export interface OrganizationSDOData {
  tdoId?: string;
  [key: string]: unknown;
}

export interface OrganizationSDORecord {
  id: string;
  data: OrganizationSDOData;
}

export interface FetchOrganizationSDOsResponse {
  structuredDataObjects: {
    records: OrganizationSDORecord[];
  };
}

// TDO Object Types for Landing Page
export interface TDOObject {
  id: string;
  name?: string;
  status?: string;
  thumbnailUrl?: string;
  createdDateTime?: string;
  modifiedDateTime?: string;
  startDateTime?: string;
  stopDateTime?: string;
  details?: unknown;
  tasks?: {
    records: Array<{
      id: string;
      status: string;
      createdDateTime: string;
      startedDateTime?: string;
      completedDateTime?: string;
      engineId?: string;
      engine?: {
        id: string;
        categoryId: string;
        name: string;
      };
      jobId?: string;
      job?: {
        status: string;
      };
    }>;
  };
  assets?: {
    count: number;
  };
}

// Login Response Types
export interface LoginResponse {
  token: string;
  userId: string;
  organization?: {
    organizationId: string;
  };
}

// Cypress Request Types
export interface CypressRequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  headers?: Record<string, string>;
  body?: unknown;
  form?: boolean;
  timeout?: number;
}

// Intercept Request Types
export interface InterceptRequest {
  url: string;
  body?: {
    query?: string;
    variables?: Record<string, unknown>;
  };
  alias?: string;
  continue: () => void;
}
