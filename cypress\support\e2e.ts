// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Cypress types are configured globally in tsconfig.json

// Import commands.js using ES2015 syntax:
import './commands';
import './helper';
import './drawUDR';

// // Alternatively you can use CommonJS syntax:
// // require('./commands')
// require('@cypress/xpath');

Cypress.on('uncaught:exception', (err, _runnable) => {
  // we expect a 3rd party library error with message 'list not defined'
  // and don't want to fail the test so we return false
  if (err.message.search(/reading 'useState'/)) {
    return false;
  }
});
